import { json } from "@sveltejs/kit"
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./$types"

// Types for the job data
interface JobData {
  title: string
  description: string
  startDate?: string
  endDate?: string
  location?: string
  skills?: string[]
  company?: string
  contactEmail?: string
  rate?: string
  jobType?: string
  originalContent?: string
  fileName?: string
}

// Cinode API configuration - these should be environment variables
const CINODE_API_BASE = process.env.CINODE_API_BASE || "https://api.cinode.com"
const CINODE_API_TOKEN = process.env.CINODE_APP_KEY
const CINODE_COMPANY_ID = process.env.CINODE_COMPANY_ID

export const POST: RequestHandler = async ({ request }) => {
  try {
    const jobData: JobData = await request.json()

    // Validate required fields
    if (!jobData.title || !jobData.description) {
      return json(
        { error: "Title and description are required" },
        { status: 400 }
      )
    }

    // Check if Cinode credentials are configured
    if (!CINODE_API_TOKEN || !CINODE_COMPANY_ID) {
      console.warn(
        "Cinode API credentials not configured. Simulating upload..."
      )

      // Simulate API call for development
      await new Promise((resolve) => setTimeout(resolve, 1000))

      return json({
        success: true,
        message: "Job uploaded successfully (simulated)",
        projectId: "sim-" + Date.now(),
        data: jobData,
      })
    }

    // Prepare the payload for Cinode API
    const cinodePayload = {
      title: jobData.title,
      description: jobData.description,
      // Map our fields to Cinode's expected format
      startDate: jobData.startDate
        ? new Date(jobData.startDate).toISOString()
        : undefined,
      endDate: jobData.endDate
        ? new Date(jobData.endDate).toISOString()
        : undefined,
      location: jobData.location,
      skills: jobData.skills,
      companyName: jobData.company,
      contactEmail: jobData.contactEmail,
      rate: jobData.rate,
      employmentType: jobData.jobType,
      // Additional metadata
      metadata: {
        uploadedVia: "SvelteKit Job Uploader",
        originalFileName: jobData.fileName,
        uploadTimestamp: new Date().toISOString(),
      },
    }

    // Make the API call to Cinode
    const response = await fetch(
      `${CINODE_API_BASE}/v0.1/companies/${CINODE_COMPANY_ID}/projects`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${CINODE_API_TOKEN}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify(cinodePayload),
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      console.error("Cinode API error:", response.status, errorText)

      return json(
        {
          error: `Cinode API error: ${response.status} ${response.statusText}`,
          details: errorText,
        },
        { status: response.status }
      )
    }

    const result = await response.json()

    return json({
      success: true,
      message: "Job uploaded successfully to Cinode",
      projectId: result.id,
      cinodeResponse: result,
    })
  } catch (error) {
    console.error("Upload error:", error)

    return json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}

// Optional: Handle file attachments
export const PUT: RequestHandler = async ({ request, url }) => {
  try {
    const projectId = url.searchParams.get("projectId")

    if (!projectId) {
      return json({ error: "Project ID is required" }, { status: 400 })
    }

    if (!CINODE_API_TOKEN || !CINODE_COMPANY_ID) {
      return json({
        success: true,
        message: "File attachment simulated",
        projectId,
      })
    }

    // Handle file upload to existing project
    const formData = await request.formData()
    const file = formData.get("file") as File

    if (!file) {
      return json({ error: "No file provided" }, { status: 400 })
    }

    // Create FormData for Cinode API
    const cinodeFormData = new FormData()
    cinodeFormData.append("Files", file)
    cinodeFormData.append("Title", file.name)
    cinodeFormData.append("Description", `Uploaded via SvelteKit Job Uploader`)

    const response = await fetch(
      `${CINODE_API_BASE}/v0.1/companies/${CINODE_COMPANY_ID}/projects/${projectId}/attachments`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${CINODE_API_TOKEN}`,
        },
        body: cinodeFormData,
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      console.error("Cinode file upload error:", response.status, errorText)

      return json(
        {
          error: `File upload failed: ${response.status} ${response.statusText}`,
          details: errorText,
        },
        { status: response.status }
      )
    }

    const result = await response.json()

    return json({
      success: true,
      message: "File uploaded successfully",
      attachmentId: result.id,
    })
  } catch (error) {
    console.error("File upload error:", error)

    return json(
      {
        error: "File upload failed",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}
