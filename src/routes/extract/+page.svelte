<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	
	let jobContent = '';
	let fileName = '';
	
	// Form fields for extracted data
	let title = '';
	let description = '';
	let startDate = '';
	let endDate = '';
	let location = '';
	let company = '';
	let rate = '';
	
	let isExtracting = false;
	let isUploading = false;
	
	onMount(() => {
		// Get content from previous page
		jobContent = sessionStorage.getItem('jobContent') || '';
		fileName = sessionStorage.getItem('uploadedFileName') || '';
		
		if (!jobContent) {
			goto('/upload');
			return;
		}
		
		// Auto-extract information
		extractInformation();
	});
	
	function extractInformation() {
		isExtracting = true;
		
		// Simple text extraction logic - this can be enhanced with AI/ML later
		
		// Extract title (look for common patterns in English and Swedish)
		const titlePatterns = [
			// English patterns
			/job title[:\s]+([^\n]+)/i,
			/position[:\s]+([^\n]+)/i,
			/role[:\s]+([^\n]+)/i,
			/^([^\n]+developer[^\n]*)/im,
			/^([^\n]+engineer[^\n]*)/im,
			/^([^\n]+manager[^\n]*)/im,

			// Swedish patterns
			/titel[:\s]+([^\n]+)/i,
			/befattning[:\s]+([^\n]+)/i,
			/tjänst[:\s]+([^\n]+)/i,
			/uppdrag[:\s]+([^\n]+)/i,
			/^([^\n]+utvecklare[^\n]*)/im,
			/^([^\n]+ingenjör[^\n]*)/im,
			/^([^\n]+chef[^\n]*)/im,
			/^([^\n]+konsult[^\n]*)/im
		];
		
		for (const pattern of titlePatterns) {
			const match = jobContent.match(pattern);
			if (match && match[1]) {
				title = match[1].trim();
				break;
			}
		}
		
		// Extract dates (English and Swedish)
		const datePatterns = [
			// English patterns
			/start date[:\s]+([^\n]+)/i,
			/starting[:\s]+([^\n]+)/i,
			/from[:\s]+([^\n]+)/i,
			/begins?[:\s]+([^\n]+)/i,

			// Swedish patterns
			/startdatum[:\s]+([^\n]+)/i,
			/start[:\s]+([^\n]+)/i,
			/börjar[:\s]+([^\n]+)/i,
			/från[:\s]+([^\n]+)/i,
			/påbörjas[:\s]+([^\n]+)/i
		];

		for (const pattern of datePatterns) {
			const match = jobContent.match(pattern);
			if (match && match[1]) {
				startDate = extractDate(match[1]);
				break;
			}
		}

		const endDatePatterns = [
			// English patterns
			/end date[:\s]+([^\n]+)/i,
			/until[:\s]+([^\n]+)/i,
			/to[:\s]+([^\n]+)/i,
			/ends?[:\s]+([^\n]+)/i,

			// Swedish patterns
			/slutdatum[:\s]+([^\n]+)/i,
			/slut[:\s]+([^\n]+)/i,
			/till[:\s]+([^\n]+)/i,
			/avslutas[:\s]+([^\n]+)/i,
			/varaktighet[:\s]+([^\n]+)/i
		];

		for (const pattern of endDatePatterns) {
			const match = jobContent.match(pattern);
			if (match && match[1]) {
				endDate = extractDate(match[1]);
				break;
			}
		}
		
		// Extract location (English and Swedish)
		const locationPatterns = [
			// English patterns
			/location[:\s]+([^\n]+)/i,
			/based in[:\s]+([^\n]+)/i,
			/office[:\s]+([^\n]+)/i,
			/workplace[:\s]+([^\n]+)/i,
			/city[:\s]+([^\n]+)/i,

			// Swedish patterns
			/plats[:\s]+([^\n]+)/i,
			/ort[:\s]+([^\n]+)/i,
			/stad[:\s]+([^\n]+)/i,
			/arbetsplats[:\s]+([^\n]+)/i,
			/kontor[:\s]+([^\n]+)/i,
			/belägen[:\s]+([^\n]+)/i,
			/placering[:\s]+([^\n]+)/i
		];

		for (const pattern of locationPatterns) {
			const match = jobContent.match(pattern);
			if (match && match[1]) {
				location = match[1].trim();
				break;
			}
		}
		
		// Extract company (English and Swedish)
		const companyPatterns = [
			// English patterns
			/company[:\s]+([^\n]+)/i,
			/employer[:\s]+([^\n]+)/i,
			/organization[:\s]+([^\n]+)/i,
			/client[:\s]+([^\n]+)/i,
			/firm[:\s]+([^\n]+)/i,

			// Swedish patterns
			/företag[:\s]+([^\n]+)/i,
			/bolag[:\s]+([^\n]+)/i,
			/arbetsgivare[:\s]+([^\n]+)/i,
			/organisation[:\s]+([^\n]+)/i,
			/kund[:\s]+([^\n]+)/i,
			/uppdragsgivare[:\s]+([^\n]+)/i
		];

		for (const pattern of companyPatterns) {
			const match = jobContent.match(pattern);
			if (match && match[1]) {
				company = match[1].trim();
				break;
			}
		}

		// Extract rate/salary information (English and Swedish)
		const ratePatterns = [
			// English patterns
			/rate[:\s]+([^\n]+)/i,
			/salary[:\s]+([^\n]+)/i,
			/compensation[:\s]+([^\n]+)/i,
			/hourly[:\s]+([^\n]+)/i,
			/pay[:\s]+([^\n]+)/i,
			/wage[:\s]+([^\n]+)/i,

			// Swedish patterns
			/arvode[:\s]+([^\n]+)/i,
			/lön[:\s]+([^\n]+)/i,
			/ersättning[:\s]+([^\n]+)/i,
			/timersättning[:\s]+([^\n]+)/i,
			/timlön[:\s]+([^\n]+)/i,
			/pris[:\s]+([^\n]+)/i,
			/kostnad[:\s]+([^\n]+)/i,

			// Currency patterns (works for both languages)
			/(\d+\s*SEK[^\n]*)/i,
			/(\d+\s*kr[^\n]*)/i,
			/(\d+\s*kronor[^\n]*)/i,
			/(\d+\s*EUR[^\n]*)/i,
			/(\d+\s*USD[^\n]*)/i,
			/(\d+\s*[^\n]*tim[^\n]*)/i, // "per timme" or "per hour"
			/(\d+\s*[^\n]*månad[^\n]*)/i // "per månad" or "per month"
		];

		for (const pattern of ratePatterns) {
			const match = jobContent.match(pattern);
			if (match && match[1]) {
				rate = match[1].trim();
				break;
			}
		}



		// Use the full content as description (don't truncate)
		if (!description) {
			description = jobContent.trim();
		}
		
		isExtracting = false;
	}
	
	function extractDate(dateStr: string): string {
		// Enhanced date extraction for both English and Swedish formats
		let cleaned = dateStr.trim();

		// Handle Swedish month names
		const swedishMonths: { [key: string]: string } = {
			'januari': '01', 'jan': '01',
			'februari': '02', 'feb': '02',
			'mars': '03', 'mar': '03',
			'april': '04', 'apr': '04',
			'maj': '05',
			'juni': '06', 'jun': '06',
			'juli': '07', 'jul': '07',
			'augusti': '08', 'aug': '08',
			'september': '09', 'sep': '09',
			'oktober': '10', 'okt': '10',
			'november': '11', 'nov': '11',
			'december': '12', 'dec': '12'
		};

		// Replace Swedish month names with numbers
		for (const [swedish, number] of Object.entries(swedishMonths)) {
			const regex = new RegExp(swedish, 'gi');
			cleaned = cleaned.replace(regex, number);
		}

		// Handle various date formats
		// YYYY-MM-DD, DD/MM/YYYY, DD.MM.YYYY, DD-MM-YYYY
		const datePatterns = [
			/(\d{4})-(\d{1,2})-(\d{1,2})/,  // YYYY-MM-DD
			/(\d{1,2})[\/\.\-](\d{1,2})[\/\.\-](\d{4})/, // DD/MM/YYYY
			/(\d{1,2})\s+(\d{1,2})\s+(\d{4})/ // DD MM YYYY
		];

		for (const pattern of datePatterns) {
			const match = cleaned.match(pattern);
			if (match) {
				let year, month, day;

				if (pattern === datePatterns[0]) {
					// YYYY-MM-DD format
					[, year, month, day] = match;
				} else {
					// DD/MM/YYYY or DD MM YYYY format
					[, day, month, year] = match;
				}

				// Ensure two-digit month and day
				month = month.padStart(2, '0');
				day = day.padStart(2, '0');

				const dateString = `${year}-${month}-${day}`;
				const date = new Date(dateString);

				if (!isNaN(date.getTime())) {
					return dateString;
				}
			}
		}

		// Fallback: try to parse as-is
		const fallbackCleaned = cleaned.replace(/[^\d\/\-\.]/g, '');
		const date = new Date(fallbackCleaned);
		if (!isNaN(date.getTime())) {
			return date.toISOString().split('T')[0];
		}

		return '';
	}
	
	async function uploadToCinode() {
		if (!title || !description) {
			alert('Please fill in at least the title and description');
			return;
		}
		
		isUploading = true;
		
		try {
			const jobData = {
				title,
				description,
				startDate,
				endDate,
				location,
				company,
				rate,
				originalContent: jobContent,
				fileName
			};
			
			const response = await fetch('/api/cinode/upload', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(jobData)
			});
			
			if (response.ok) {
				await response.json();
				alert('Job successfully uploaded to Cinode!');
				sessionStorage.removeItem('jobContent');
				sessionStorage.removeItem('uploadedFileName');
				goto('/');
			} else {
				const error = await response.text();
				alert(`Upload failed: ${error}`);
			}
		} catch (error) {
			console.error('Upload error:', error);
			alert('Upload failed. Please try again.');
		} finally {
			isUploading = false;
		}
	}
	
	function goBack() {
		goto('/upload');
	}
</script>

<div class="container">
	<h1>Extract Job Information</h1>
	<p>Review and edit the extracted information before uploading to Cinode</p>
	
	{#if fileName}
		<div class="file-info">
			<p>📄 Source: {fileName}</p>
		</div>
	{/if}
	
	{#if isExtracting}
		<div class="loading">
			<p>🔍 Extracting information...</p>
		</div>
	{:else}
		<form on:submit|preventDefault={uploadToCinode}>
			<div class="form-grid">
				<div class="form-group">
					<label for="title">Job Title *</label>
					<input
						id="title"
						type="text"
						bind:value={title}
						placeholder="e.g., Senior Frontend Developer"
						required
					/>
				</div>
				
				<div class="form-group">
					<label for="company">Company</label>
					<input
						id="company"
						type="text"
						bind:value={company}
						placeholder="Company name"
					/>
				</div>

				
				<div class="form-group">
					<label for="location">Location</label>
					<input
						id="location"
						type="text"
						bind:value={location}
						placeholder="e.g., Stockholm, Sweden"
					/>
				</div>

				<div class="form-group">
					<label for="rate">Rate</label>
					<input
						id="rate"
						type="text"
						bind:value={rate}
						placeholder="e.g., 800 SEK/hour"
					/>
				</div>
				
				<div class="form-group">
					<label for="startDate">Start Date</label>
					<input
						id="startDate"
						type="date"
						bind:value={startDate}
					/>
				</div>
				
				<div class="form-group">
					<label for="endDate">End Date</label>
					<input
						id="endDate"
						type="date"
						bind:value={endDate}
					/>
				</div>
				
				<div class="form-group full-width">
					<label for="description">Description *</label>
					<textarea
						id="description"
						bind:value={description}
						placeholder="Job description and requirements"
						rows="6"
						required
					></textarea>
				</div>
				

				
			</div>
			
			<div class="actions">
				<button type="button" on:click={goBack} class="btn btn-secondary">
					← Back
				</button>
				<button type="submit" class="btn btn-primary" disabled={isUploading}>
					{isUploading ? 'Uploading...' : 'Upload to Cinode'}
				</button>
			</div>
		</form>
	{/if}
</div>

<style>
	.container {
		max-width: 900px;
		margin: 0 auto;
		padding: 20px;
	}
	
	h1 {
		color: #333;
		margin-bottom: 8px;
	}
	
	p {
		color: #666;
		margin-bottom: 30px;
	}
	
	.file-info {
		margin-bottom: 20px;
		padding: 12px;
		background: #e8f5e8;
		border-radius: 6px;
		border-left: 4px solid #22c55e;
	}
	
	.file-info p {
		margin: 0;
		color: #166534;
		font-weight: 500;
	}
	
	.loading {
		text-align: center;
		padding: 40px;
		color: #666;
	}
	
	.form-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 20px;
		margin-bottom: 30px;
	}
	
	.form-group {
		display: flex;
		flex-direction: column;
	}
	
	.form-group.full-width {
		grid-column: 1 / -1;
	}
	
	label {
		font-weight: 600;
		margin-bottom: 6px;
		color: #333;
	}
	
	input, select, textarea {
		padding: 10px 12px;
		border: 2px solid #e1e5e9;
		border-radius: 6px;
		font-family: inherit;
		font-size: 14px;
	}
	
	input:focus, select:focus, textarea:focus {
		outline: none;
		border-color: #007acc;
	}
	
	textarea {
		resize: vertical;
		min-height: 120px;
	}
	
	.actions {
		display: flex;
		gap: 12px;
		justify-content: space-between;
	}
	
	.btn {
		padding: 12px 24px;
		border: none;
		border-radius: 6px;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s;
		font-size: 14px;
	}
	
	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}
	
	.btn-secondary {
		background: #f3f4f6;
		color: #374151;
	}
	
	.btn-secondary:hover:not(:disabled) {
		background: #e5e7eb;
	}
	
	.btn-primary {
		background: #007acc;
		color: white;
	}
	
	.btn-primary:hover:not(:disabled) {
		background: #005a9e;
	}
	
	@media (max-width: 768px) {
		.form-grid {
			grid-template-columns: 1fr;
		}
		
		.actions {
			flex-direction: column;
		}
		
		.btn {
			width: 100%;
		}
	}
</style>
