<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	
	let jobContent = '';
	let fileName = '';
	
	// Form fields for extracted data
	let title = '';
	let description = '';
	let startDate = '';
	let endDate = '';
	let location = '';
	let skills = '';
	let company = '';
	let contactEmail = '';
	let rate = '';
	let jobType = 'Contract';
	
	let isExtracting = false;
	let isUploading = false;
	
	onMount(() => {
		// Get content from previous page
		jobContent = sessionStorage.getItem('jobContent') || '';
		fileName = sessionStorage.getItem('uploadedFileName') || '';
		
		if (!jobContent) {
			goto('/upload');
			return;
		}
		
		// Auto-extract information
		extractInformation();
	});
	
	function extractInformation() {
		isExtracting = true;
		
		// Simple text extraction logic - this can be enhanced with AI/ML later
		const content = jobContent.toLowerCase();
		
		// Extract title (look for common patterns)
		const titlePatterns = [
			/job title[:\s]+([^\n]+)/i,
			/position[:\s]+([^\n]+)/i,
			/role[:\s]+([^\n]+)/i,
			/^([^\n]+developer[^\n]*)/im,
			/^([^\n]+engineer[^\n]*)/im,
			/^([^\n]+manager[^\n]*)/im
		];
		
		for (const pattern of titlePatterns) {
			const match = jobContent.match(pattern);
			if (match && match[1]) {
				title = match[1].trim();
				break;
			}
		}
		
		// Extract dates
		const datePatterns = [
			/start date[:\s]+([^\n]+)/i,
			/starting[:\s]+([^\n]+)/i,
			/from[:\s]+([^\n]+)/i
		];
		
		for (const pattern of datePatterns) {
			const match = jobContent.match(pattern);
			if (match && match[1]) {
				startDate = extractDate(match[1]);
				break;
			}
		}
		
		const endDatePatterns = [
			/end date[:\s]+([^\n]+)/i,
			/until[:\s]+([^\n]+)/i,
			/to[:\s]+([^\n]+)/i
		];
		
		for (const pattern of endDatePatterns) {
			const match = jobContent.match(pattern);
			if (match && match[1]) {
				endDate = extractDate(match[1]);
				break;
			}
		}
		
		// Extract location
		const locationPatterns = [
			/location[:\s]+([^\n]+)/i,
			/based in[:\s]+([^\n]+)/i,
			/office[:\s]+([^\n]+)/i
		];
		
		for (const pattern of locationPatterns) {
			const match = jobContent.match(pattern);
			if (match && match[1]) {
				location = match[1].trim();
				break;
			}
		}
		
		// Extract company
		const companyPatterns = [
			/company[:\s]+([^\n]+)/i,
			/employer[:\s]+([^\n]+)/i,
			/organization[:\s]+([^\n]+)/i
		];

		for (const pattern of companyPatterns) {
			const match = jobContent.match(pattern);
			if (match && match[1]) {
				company = match[1].trim();
				break;
			}
		}

		// Extract rate/salary information
		const ratePatterns = [
			/rate[:\s]+([^\n]+)/i,
			/salary[:\s]+([^\n]+)/i,
			/compensation[:\s]+([^\n]+)/i,
			/hourly[:\s]+([^\n]+)/i,
			/(\d+\s*SEK[^\n]*)/i,
			/(\d+\s*EUR[^\n]*)/i,
			/(\d+\s*USD[^\n]*)/i
		];

		for (const pattern of ratePatterns) {
			const match = jobContent.match(pattern);
			if (match && match[1]) {
				rate = match[1].trim();
				break;
			}
		}

		// Extract skills
		const skillsPatterns = [
			/skills[:\s]+([^\n]+)/i,
			/technologies[:\s]+([^\n]+)/i,
			/requirements[:\s]+([^\n]+)/i,
			/experience[:\s]+([^\n]+)/i
		];

		for (const pattern of skillsPatterns) {
			const match = jobContent.match(pattern);
			if (match && match[1]) {
				skills = match[1].trim();
				break;
			}
		}

		// Use the full content as description (don't truncate)
		if (!description) {
			description = jobContent.trim();
		}
		
		isExtracting = false;
	}
	
	function extractDate(dateStr: string): string {
		// Simple date extraction - convert various formats to YYYY-MM-DD
		const cleaned = dateStr.trim().replace(/[^\d\/\-\.]/g, '');
		
		// Try to parse and format as YYYY-MM-DD
		const date = new Date(cleaned);
		if (!isNaN(date.getTime())) {
			return date.toISOString().split('T')[0];
		}
		
		return '';
	}
	
	async function uploadToCinode() {
		if (!title || !description) {
			alert('Please fill in at least the title and description');
			return;
		}
		
		isUploading = true;
		
		try {
			const jobData = {
				title,
				description,
				startDate,
				endDate,
				location,
				skills: skills.split(',').map(s => s.trim()).filter(s => s),
				company,
				contactEmail,
				rate,
				jobType,
				originalContent: jobContent,
				fileName
			};
			
			const response = await fetch('/api/cinode/upload', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(jobData)
			});
			
			if (response.ok) {
				const result = await response.json();
				alert('Job successfully uploaded to Cinode!');
				sessionStorage.removeItem('jobContent');
				sessionStorage.removeItem('uploadedFileName');
				goto('/');
			} else {
				const error = await response.text();
				alert(`Upload failed: ${error}`);
			}
		} catch (error) {
			console.error('Upload error:', error);
			alert('Upload failed. Please try again.');
		} finally {
			isUploading = false;
		}
	}
	
	function goBack() {
		goto('/upload');
	}
</script>

<div class="container">
	<h1>Extract Job Information</h1>
	<p>Review and edit the extracted information before uploading to Cinode</p>
	
	{#if fileName}
		<div class="file-info">
			<p>📄 Source: {fileName}</p>
		</div>
	{/if}
	
	{#if isExtracting}
		<div class="loading">
			<p>🔍 Extracting information...</p>
		</div>
	{:else}
		<form on:submit|preventDefault={uploadToCinode}>
			<div class="form-grid">
				<div class="form-group">
					<label for="title">Job Title *</label>
					<input
						id="title"
						type="text"
						bind:value={title}
						placeholder="e.g., Senior Frontend Developer"
						required
					/>
				</div>
				
				<div class="form-group">
					<label for="company">Company</label>
					<input
						id="company"
						type="text"
						bind:value={company}
						placeholder="Company name"
					/>
				</div>
				
				<div class="form-group">
					<label for="location">Location</label>
					<input
						id="location"
						type="text"
						bind:value={location}
						placeholder="e.g., Stockholm, Sweden"
					/>
				</div>
				
				<div class="form-group">
					<label for="jobType">Assignment Type</label>
					<select id="jobType" bind:value={jobType}>
						<option value="Contract">Contract</option>
						<option value="Freelance">Freelance</option>
						<option value="Consulting">Consulting</option>
						<option value="Full-time">Full-time</option>
						<option value="Part-time">Part-time</option>
						<option value="Internship">Internship</option>
					</select>
				</div>
				
				<div class="form-group">
					<label for="startDate">Start Date</label>
					<input
						id="startDate"
						type="date"
						bind:value={startDate}
					/>
				</div>
				
				<div class="form-group">
					<label for="endDate">End Date</label>
					<input
						id="endDate"
						type="date"
						bind:value={endDate}
					/>
				</div>
				
				<div class="form-group full-width">
					<label for="description">Description *</label>
					<textarea
						id="description"
						bind:value={description}
						placeholder="Job description and requirements"
						rows="6"
						required
					></textarea>
				</div>
				
				<div class="form-group full-width">
					<label for="skills">Skills (comma-separated)</label>
					<input
						id="skills"
						type="text"
						bind:value={skills}
						placeholder="e.g., JavaScript, React, Node.js, TypeScript"
					/>
				</div>
				
				<div class="form-group">
					<label for="contactEmail">Contact Email</label>
					<input
						id="contactEmail"
						type="email"
						bind:value={contactEmail}
						placeholder="<EMAIL>"
					/>
				</div>
				
				<div class="form-group">
					<label for="rate">Rate</label>
					<input
						id="rate"
						type="text"
						bind:value={rate}
						placeholder="e.g., 800 SEK/hour"
					/>
				</div>
			</div>
			
			<div class="actions">
				<button type="button" on:click={goBack} class="btn btn-secondary">
					← Back
				</button>
				<button type="submit" class="btn btn-primary" disabled={isUploading}>
					{isUploading ? 'Uploading...' : 'Upload to Cinode'}
				</button>
			</div>
		</form>
	{/if}
</div>

<style>
	.container {
		max-width: 900px;
		margin: 0 auto;
		padding: 20px;
	}
	
	h1 {
		color: #333;
		margin-bottom: 8px;
	}
	
	p {
		color: #666;
		margin-bottom: 30px;
	}
	
	.file-info {
		margin-bottom: 20px;
		padding: 12px;
		background: #e8f5e8;
		border-radius: 6px;
		border-left: 4px solid #22c55e;
	}
	
	.file-info p {
		margin: 0;
		color: #166534;
		font-weight: 500;
	}
	
	.loading {
		text-align: center;
		padding: 40px;
		color: #666;
	}
	
	.form-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 20px;
		margin-bottom: 30px;
	}
	
	.form-group {
		display: flex;
		flex-direction: column;
	}
	
	.form-group.full-width {
		grid-column: 1 / -1;
	}
	
	label {
		font-weight: 600;
		margin-bottom: 6px;
		color: #333;
	}
	
	input, select, textarea {
		padding: 10px 12px;
		border: 2px solid #e1e5e9;
		border-radius: 6px;
		font-family: inherit;
		font-size: 14px;
	}
	
	input:focus, select:focus, textarea:focus {
		outline: none;
		border-color: #007acc;
	}
	
	textarea {
		resize: vertical;
		min-height: 120px;
	}
	
	.actions {
		display: flex;
		gap: 12px;
		justify-content: space-between;
	}
	
	.btn {
		padding: 12px 24px;
		border: none;
		border-radius: 6px;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s;
		font-size: 14px;
	}
	
	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}
	
	.btn-secondary {
		background: #f3f4f6;
		color: #374151;
	}
	
	.btn-secondary:hover:not(:disabled) {
		background: #e5e7eb;
	}
	
	.btn-primary {
		background: #007acc;
		color: white;
	}
	
	.btn-primary:hover:not(:disabled) {
		background: #005a9e;
	}
	
	@media (max-width: 768px) {
		.form-grid {
			grid-template-columns: 1fr;
		}
		
		.actions {
			flex-direction: column;
		}
		
		.btn {
			width: 100%;
		}
	}
</style>
