<script lang="ts">
	import favicon from '$lib/assets/favicon.svg';
	import { onMount } from 'svelte';

	let { children } = $props();

	// Get current path from browser location
	let currentPath = $state('');

	onMount(() => {
		currentPath = window.location.pathname;

		// Update path on navigation
		const updatePath = () => {
			currentPath = window.location.pathname;
		};

		window.addEventListener('popstate', updatePath);

		return () => {
			window.removeEventListener('popstate', updatePath);
		};
	});
</script>

<svelte:head>
	<link rel="icon" href={favicon} />
	<title>Cinode Job Uploader</title>
</svelte:head>

<div class="app">
	<header>
		<nav>
			<a href="/" class="logo">Cinode Job Uploader</a>
			<div class="nav-links">
				<a href="/" class:active={currentPath === '/'}>Home</a>
				<a href="/upload" class:active={currentPath === '/upload'}>Upload</a>
			</div>
		</nav>
	</header>

	<main>
		{@render children?.()}
	</main>

	<footer>
		<p>&copy; 2024 Cinode Job Uploader</p>
	</footer>
</div>

<style>
	:global(body) {
		margin: 0;
		padding: 0;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
		background: #fafafa;
		color: #333;
		line-height: 1.6;
	}

	:global(*) {
		box-sizing: border-box;
	}

	.app {
		min-height: 100vh;
		display: flex;
		flex-direction: column;
	}

	header {
		background: white;
		border-bottom: 1px solid #e1e5e9;
		padding: 0 20px;
	}

	nav {
		max-width: 1200px;
		margin: 0 auto;
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 60px;
	}

	.logo {
		font-weight: 700;
		font-size: 18px;
		color: #007acc;
		text-decoration: none;
	}

	.nav-links {
		display: flex;
		gap: 24px;
	}

	.nav-links a {
		color: #666;
		text-decoration: none;
		font-weight: 500;
		transition: color 0.2s;
	}

	.nav-links a:hover,
	.nav-links a.active {
		color: #007acc;
	}

	main {
		flex: 1;
		padding: 20px;
	}

	footer {
		background: white;
		border-top: 1px solid #e1e5e9;
		padding: 20px;
		text-align: center;
		color: #666;
		font-size: 14px;
	}

	footer p {
		margin: 0;
	}

	@media (max-width: 640px) {
		nav {
			flex-direction: column;
			height: auto;
			padding: 16px 0;
			gap: 16px;
		}

		.nav-links {
			gap: 16px;
		}
	}
</style>
