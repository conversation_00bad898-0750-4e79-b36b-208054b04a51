<script lang="ts">
	import { goto } from '$app/navigation';
	
	let textContent = '';
	let isDragOver = false;
	let uploadedFile: File | null = null;
	let fileName = '';
	
	function handleDragOver(event: DragEvent) {
		event.preventDefault();
		isDragOver = true;
	}
	
	function handleDragLeave(event: DragEvent) {
		event.preventDefault();
		isDragOver = false;
	}
	
	function handleDrop(event: DragEvent) {
		event.preventDefault();
		isDragOver = false;
		
		const files = event.dataTransfer?.files;
		if (files && files.length > 0) {
			handleFileUpload(files[0]);
		}
	}
	
	function handleFileInput(event: Event) {
		const target = event.target as HTMLInputElement;
		const files = target.files;
		if (files && files.length > 0) {
			handleFileUpload(files[0]);
		}
	}
	
	async function handleFileUpload(file: File) {
		uploadedFile = file;
		fileName = file.name;
		
		// Read file content based on type
		if (file.type === 'text/plain') {
			const text = await file.text();
			textContent = text;
		} else if (file.type === 'application/pdf') {
			// For now, just show the file name - we'll implement PDF parsing later
			textContent = `PDF file uploaded: ${file.name}\n\n[PDF content will be extracted automatically]`;
		} else {
			textContent = `File uploaded: ${file.name}\n\n[File content will be processed]`;
		}
	}
	
	function proceedToExtraction() {
		if (!textContent.trim() && !uploadedFile) {
			alert('Please enter text or upload a file first');
			return;
		}
		
		// Store the content in sessionStorage to pass to next page
		sessionStorage.setItem('jobContent', textContent);
		if (uploadedFile) {
			sessionStorage.setItem('uploadedFileName', fileName);
		}
		
		goto('/extract');
	}
	
	function clearContent() {
		textContent = '';
		uploadedFile = null;
		fileName = '';
	}
</script>

<div class="container">
	<h1>Upload Job Assignment</h1>
	<p>Paste job description text or upload a file to get started</p>
	
	<div class="upload-section">
		<!-- Text Input Area -->
		<div class="text-input-section">
			<label for="job-text">Job Description Text:</label>
			<textarea
				id="job-text"
				bind:value={textContent}
				placeholder="Paste your job description here or upload a file below..."
				rows="10"
			></textarea>
		</div>
		
		<!-- File Upload Area -->
		<div class="file-upload-section">
			<div
				class="drop-zone"
				class:drag-over={isDragOver}
				on:dragover={handleDragOver}
				on:dragleave={handleDragLeave}
				on:drop={handleDrop}
				role="button"
				tabindex="0"
			>
				<div class="drop-zone-content">
					<svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
						<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
						<polyline points="14,2 14,8 20,8"></polyline>
						<line x1="16" y1="13" x2="8" y2="13"></line>
						<line x1="16" y1="17" x2="8" y2="17"></line>
						<polyline points="10,9 9,9 8,9"></polyline>
					</svg>
					<p>Drag and drop files here</p>
					<p class="or-text">or</p>
					<label for="file-input" class="file-input-label">
						Choose File
						<input
							id="file-input"
							type="file"
							accept=".txt,.pdf,.doc,.docx"
							on:change={handleFileInput}
							hidden
						/>
					</label>
				</div>
			</div>
			
			{#if fileName}
				<div class="file-info">
					<p>📄 {fileName}</p>
				</div>
			{/if}
		</div>
	</div>
	
	<!-- Action Buttons -->
	<div class="actions">
		<button type="button" on:click={clearContent} class="btn btn-secondary">
			Clear
		</button>
		<button type="button" on:click={proceedToExtraction} class="btn btn-primary">
			Extract Information →
		</button>
	</div>
</div>

<style>
	.container {
		max-width: 800px;
		margin: 0 auto;
		padding: 20px;
	}
	
	h1 {
		color: #333;
		margin-bottom: 8px;
	}
	
	p {
		color: #666;
		margin-bottom: 30px;
	}
	
	.upload-section {
		display: grid;
		gap: 30px;
		margin-bottom: 30px;
	}
	
	.text-input-section label {
		display: block;
		font-weight: 600;
		margin-bottom: 8px;
		color: #333;
	}
	
	textarea {
		width: 100%;
		padding: 12px;
		border: 2px solid #e1e5e9;
		border-radius: 8px;
		font-family: inherit;
		font-size: 14px;
		resize: vertical;
		min-height: 200px;
	}
	
	textarea:focus {
		outline: none;
		border-color: #007acc;
	}
	
	.drop-zone {
		border: 2px dashed #d1d5db;
		border-radius: 12px;
		padding: 40px 20px;
		text-align: center;
		transition: all 0.2s ease;
		cursor: pointer;
		background: #fafafa;
	}
	
	.drop-zone:hover,
	.drop-zone.drag-over {
		border-color: #007acc;
		background: #f0f8ff;
	}
	
	.drop-zone-content svg {
		color: #9ca3af;
		margin-bottom: 16px;
	}
	
	.drop-zone-content p {
		margin: 8px 0;
		color: #6b7280;
	}
	
	.or-text {
		font-size: 14px;
		color: #9ca3af;
		margin: 16px 0 !important;
	}
	
	.file-input-label {
		display: inline-block;
		padding: 10px 20px;
		background: #007acc;
		color: white;
		border-radius: 6px;
		cursor: pointer;
		transition: background 0.2s;
		font-weight: 500;
	}
	
	.file-input-label:hover {
		background: #005a9e;
	}
	
	.file-info {
		margin-top: 16px;
		padding: 12px;
		background: #e8f5e8;
		border-radius: 6px;
		border-left: 4px solid #22c55e;
	}
	
	.file-info p {
		margin: 0;
		color: #166534;
		font-weight: 500;
	}
	
	.actions {
		display: flex;
		gap: 12px;
		justify-content: flex-end;
	}
	
	.btn {
		padding: 12px 24px;
		border: none;
		border-radius: 6px;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s;
		font-size: 14px;
	}
	
	.btn-secondary {
		background: #f3f4f6;
		color: #374151;
	}
	
	.btn-secondary:hover {
		background: #e5e7eb;
	}
	
	.btn-primary {
		background: #007acc;
		color: white;
	}
	
	.btn-primary:hover {
		background: #005a9e;
	}
	
	@media (max-width: 640px) {
		.container {
			padding: 16px;
		}
		
		.actions {
			flex-direction: column;
		}
		
		.btn {
			width: 100%;
		}
	}
</style>
