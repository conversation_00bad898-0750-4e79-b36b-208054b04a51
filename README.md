# Cinode Job Assignment Uploader

A SvelteKit application for uploading job assignments to Cinode. This tool allows you to paste job descriptions or upload files, extract relevant information, and post them directly to your Cinode projects.

## Features

- **Text Input**: Paste job descriptions directly into a text area
- **File Upload**: Drag and drop or select files (TXT, PDF, DOC, DOCX)
- **Smart Extraction**: Automatically extracts job details like title, dates, location, etc.
- **Form Editing**: Review and edit extracted information before upload
- **Cinode Integration**: Direct API integration with Cinode projects endpoint

## Setup

1. **Install Dependencies**

   ```bash
   npm install
   ```

2. **Configure Cinode API**

   Copy the environment example file:

   ```bash
   cp .env.example .env
   ```

   Edit `.env` and add your Cinode credentials:

   ```env
   CINODE_API_BASE=https://api.cinode.com
   CINODE_APP_KEY=your_app_key_here
   CINODE_COMPANY_ID=your_company_id_here
   ```

3. **Start Development Server**
   ```bash
   npm run dev
   ```

## Usage

1. **Navigate to Upload Page**: Click "Start Upload" from the homepage
2. **Add Content**: Either paste text or upload a file containing the job description
3. **Extract Information**: Click "Extract Information" to parse the content
4. **Review & Edit**: Modify the extracted fields as needed
5. **Upload to Cinode**: Click "Upload to Cinode" to create the project

## API Endpoints

### POST `/api/cinode/upload`

Uploads job data to Cinode projects endpoint.

**Request Body:**

```json
{
  "title": "Job Title",
  "description": "Job description",
  "startDate": "2024-01-01",
  "endDate": "2024-12-31",
  "location": "Stockholm, Sweden",
  "skills": ["JavaScript", "React"],
  "company": "Company Name",
  "contactEmail": "<EMAIL>",
  "rate": "800 SEK/hour",
  "jobType": "Contract"
}
```

### PUT `/api/cinode/upload?projectId=123`

Uploads file attachments to existing Cinode project.

## Development

The application is built with:

- **SvelteKit** - Full-stack framework
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server

### Project Structure

```
src/
├── routes/
│   ├── +page.svelte          # Homepage
│   ├── upload/+page.svelte   # Upload page
│   ├── extract/+page.svelte  # Information extraction page
│   └── api/cinode/upload/    # API endpoints
├── lib/                      # Shared components
└── app.html                  # HTML template
```

## Building for Production

```bash
npm run build
npm run preview
```

## Environment Variables

| Variable            | Description             | Required |
| ------------------- | ----------------------- | -------- |
| `CINODE_API_BASE`   | Cinode API base URL     | Yes      |
| `CINODE_APP_KEY`    | Your Cinode API app key | Yes      |
| `CINODE_COMPANY_ID` | Your Cinode company ID  | Yes      |

## Notes

- Without API credentials, the app will simulate uploads for development
- File parsing currently supports basic text extraction
- PDF parsing can be enhanced with additional libraries
- The extraction logic uses simple regex patterns and can be improved with AI/ML
